"""
Setup script for the CLIP-based Shoplifting Detection System.
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """Install required dependencies."""
    logger.info("Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    logger.info("Creating directories...")
    
    directories = [
        "detection_results",
        "detection_results/frames",
        "detection_results/reports",
        "detection_results/timelines"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")
    
    return True


def run_tests():
    """Run installation tests."""
    logger.info("Running installation tests...")
    
    try:
        result = subprocess.run([sys.executable, "test_installation.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("All tests passed!")
            return True
        else:
            logger.error("Some tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Failed to run tests: {e}")
        return False


def main():
    """Main setup function."""
    print("="*60)
    print("CLIP-BASED SHOPLIFTING DETECTION SYSTEM SETUP")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Run tests
    if not run_tests():
        logger.warning("Setup completed but some tests failed")
        logger.info("You may still be able to use the system")
    else:
        logger.info("Setup completed successfully!")
    
    print("\n" + "="*60)
    print("SETUP COMPLETE")
    print("="*60)
    print("\nNext steps:")
    print("1. Run 'python example_usage.py' to see usage examples")
    print("2. Run 'python main.py --help' to see all options")
    print("3. Run 'python main.py your_video.mp4' to analyze a video")
    print("\nFor more information, see README.md")


if __name__ == "__main__":
    main()
