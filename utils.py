"""
Utility functions for the shoplifting detection system.
"""

import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.patches as patches

logger = logging.getLogger(__name__)


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in bytes to human-readable string.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024**2:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024**3:
        return f"{size_bytes/(1024**2):.1f} MB"
    else:
        return f"{size_bytes/(1024**3):.1f} GB"


def create_detection_timeline(results: Dict, output_path: str = None) -> str:
    """
    Create a timeline visualization of detection results.
    
    Args:
        results: Detection results dictionary
        output_path: Output file path for the timeline image
        
    Returns:
        Path to the saved timeline image
    """
    detections = results['detections']
    video_info = results['video_info']
    
    if not detections:
        logger.warning("No detections to visualize")
        return None
    
    # Extract data for plotting
    timestamps = [d['timestamp'] for d in detections]
    confidences = [d['confidence'] for d in detections]
    is_suspicious = [d['is_shoplifting'] for d in detections]
    
    # Create figure
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
    
    # Plot confidence scores
    colors = ['red' if sus else 'green' for sus in is_suspicious]
    ax1.scatter(timestamps, confidences, c=colors, alpha=0.6, s=20)
    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax1.set_ylabel('Confidence Score')
    ax1.set_title('Shoplifting Detection Timeline')
    ax1.grid(True, alpha=0.3)
    ax1.legend(['Threshold', 'Suspicious', 'Normal'], 
               handles=[plt.Line2D([0], [0], color='black', linestyle='--'),
                       plt.Line2D([0], [0], marker='o', color='red', linestyle=''),
                       plt.Line2D([0], [0], marker='o', color='green', linestyle='')])
    
    # Plot suspicious periods
    suspicious_periods = results['analysis']['suspicious_periods']
    for period in suspicious_periods:
        ax2.barh(0, period['duration'], left=period['start_time'], 
                height=0.5, color='red', alpha=0.7)
        # Add confidence text
        mid_time = period['start_time'] + period['duration'] / 2
        ax2.text(mid_time, 0, f"{period['max_confidence']:.2f}", 
                ha='center', va='center', fontsize=8, fontweight='bold')
    
    ax2.set_ylabel('Suspicious\nPeriods')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_ylim(-0.5, 0.5)
    ax2.set_yticks([])
    ax2.grid(True, alpha=0.3)
    
    # Set x-axis limits
    max_time = video_info['duration_seconds']
    ax1.set_xlim(0, max_time)
    ax2.set_xlim(0, max_time)
    
    plt.tight_layout()
    
    # Save figure
    if output_path is None:
        video_name = os.path.splitext(os.path.basename(results['video_path']))[0]
        output_path = f"timeline_{video_name}.png"
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Timeline saved to: {output_path}")
    return output_path


def create_detection_report(results: Dict, output_path: str = None) -> str:
    """
    Create a detailed HTML report of detection results.
    
    Args:
        results: Detection results dictionary
        output_path: Output file path for the HTML report
        
    Returns:
        Path to the saved HTML report
    """
    if output_path is None:
        video_name = os.path.splitext(os.path.basename(results['video_path']))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"report_{video_name}_{timestamp}.html"
    
    analysis = results['analysis']
    video_info = results['video_info']
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Shoplifting Detection Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
            .section {{ margin: 20px 0; }}
            .metric {{ display: inline-block; margin: 10px; padding: 10px; 
                      background-color: #e8f4f8; border-radius: 5px; }}
            .risk-high {{ background-color: #ffebee; }}
            .risk-medium {{ background-color: #fff3e0; }}
            .risk-low {{ background-color: #e8f5e8; }}
            .risk-none {{ background-color: #f5f5f5; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Shoplifting Detection Report</h1>
            <p><strong>Video:</strong> {os.path.basename(results['video_path'])}</p>
            <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="section">
            <h2>Video Information</h2>
            <div class="metric">
                <strong>Duration:</strong> {format_duration(video_info['duration_seconds'])}
            </div>
            <div class="metric">
                <strong>Resolution:</strong> {video_info['width']}x{video_info['height']}
            </div>
            <div class="metric">
                <strong>FPS:</strong> {video_info['fps']:.1f}
            </div>
            <div class="metric">
                <strong>File Size:</strong> {format_file_size(int(video_info['file_size_mb'] * 1024 * 1024))}
            </div>
        </div>
        
        <div class="section">
            <h2>Detection Summary</h2>
            <div class="metric risk-{analysis['risk_level'].lower()}">
                <strong>Risk Level:</strong> {analysis['risk_level']}
            </div>
            <div class="metric">
                <strong>Frames Processed:</strong> {analysis['total_frames']}
            </div>
            <div class="metric">
                <strong>Suspicious Frames:</strong> {analysis['suspicious_frames']}
            </div>
            <div class="metric">
                <strong>Suspicion Rate:</strong> {analysis['suspicion_rate']:.2%}
            </div>
            <div class="metric">
                <strong>Max Confidence:</strong> {analysis['max_confidence']:.3f}
            </div>
        </div>
    """
    
    # Add suspicious periods table
    if analysis['suspicious_periods']:
        html_content += """
        <div class="section">
            <h2>Suspicious Periods</h2>
            <table>
                <tr>
                    <th>Period</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>Duration</th>
                    <th>Frames</th>
                    <th>Max Confidence</th>
                </tr>
        """
        
        for i, period in enumerate(analysis['suspicious_periods'], 1):
            start_time = format_duration(period['start_time'])
            end_time = format_duration(period['end_time'])
            duration = format_duration(period['duration'])
            
            html_content += f"""
                <tr>
                    <td>{i}</td>
                    <td>{start_time}</td>
                    <td>{end_time}</td>
                    <td>{duration}</td>
                    <td>{period['frame_count']}</td>
                    <td>{period['max_confidence']:.3f}</td>
                </tr>
            """
        
        html_content += """
            </table>
        </div>
        """
    
    html_content += """
    </body>
    </html>
    """
    
    with open(output_path, 'w') as f:
        f.write(html_content)
    
    logger.info(f"HTML report saved to: {output_path}")
    return output_path


def validate_video_file(video_path: str) -> bool:
    """
    Validate if a video file exists and is accessible.
    
    Args:
        video_path: Path to the video file
        
    Returns:
        True if valid, False otherwise
    """
    if not os.path.exists(video_path):
        logger.error(f"Video file does not exist: {video_path}")
        return False
    
    if not os.path.isfile(video_path):
        logger.error(f"Path is not a file: {video_path}")
        return False
    
    # Check file extension
    _, ext = os.path.splitext(video_path.lower())
    from config import SUPPORTED_VIDEO_FORMATS
    if ext not in SUPPORTED_VIDEO_FORMATS:
        logger.error(f"Unsupported video format: {ext}")
        return False
    
    return True


def batch_process_directory(directory_path: str, recursive: bool = False) -> List[str]:
    """
    Find all video files in a directory.
    
    Args:
        directory_path: Path to the directory
        recursive: Whether to search recursively
        
    Returns:
        List of video file paths
    """
    if not os.path.exists(directory_path):
        logger.error(f"Directory does not exist: {directory_path}")
        return []
    
    video_files = []
    from config import SUPPORTED_VIDEO_FORMATS
    
    if recursive:
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                _, ext = os.path.splitext(file.lower())
                if ext in SUPPORTED_VIDEO_FORMATS:
                    video_files.append(os.path.join(root, file))
    else:
        for file in os.listdir(directory_path):
            file_path = os.path.join(directory_path, file)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(file.lower())
                if ext in SUPPORTED_VIDEO_FORMATS:
                    video_files.append(file_path)
    
    return sorted(video_files)


def merge_detection_results(results_list: List[Dict]) -> Dict:
    """
    Merge multiple detection results into a summary.
    
    Args:
        results_list: List of detection result dictionaries
        
    Returns:
        Merged summary dictionary
    """
    if not results_list:
        return {}
    
    total_videos = len(results_list)
    total_frames = sum(r['analysis']['total_frames'] for r in results_list)
    total_suspicious = sum(r['analysis']['suspicious_frames'] for r in results_list)
    
    risk_levels = [r['analysis']['risk_level'] for r in results_list]
    high_risk_count = risk_levels.count('HIGH')
    medium_risk_count = risk_levels.count('MEDIUM')
    low_risk_count = risk_levels.count('LOW')
    no_risk_count = risk_levels.count('NONE')
    
    summary = {
        'total_videos': total_videos,
        'total_frames_processed': total_frames,
        'total_suspicious_frames': total_suspicious,
        'overall_suspicion_rate': total_suspicious / total_frames if total_frames > 0 else 0,
        'risk_distribution': {
            'HIGH': high_risk_count,
            'MEDIUM': medium_risk_count,
            'LOW': low_risk_count,
            'NONE': no_risk_count
        },
        'high_risk_videos': [
            {
                'path': r['video_path'],
                'suspicion_rate': r['analysis']['suspicion_rate'],
                'max_confidence': r['analysis']['max_confidence']
            }
            for r in results_list if r['analysis']['risk_level'] == 'HIGH'
        ],
        'processing_timestamp': datetime.now().isoformat()
    }
    
    return summary
