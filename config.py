"""
Configuration file for the CLIP-based shoplifting detection system.
"""

import os

# Model Configuration
CLIP_MODEL_NAME = "zer0int/CLIP-GmP-ViT-L-14"
DEVICE = "cuda" if os.environ.get("CUDA_AVAILABLE", "false").lower() == "true" else "cpu"

# Video Processing Configuration
FRAME_EXTRACTION_RATE = 1  # Extract every N frames (1 = every frame)
MAX_FRAMES_PER_VIDEO = 300  # Maximum frames to process per video
FRAME_RESIZE_WIDTH = 224
FRAME_RESIZE_HEIGHT = 224

# Detection Configuration
CONFIDENCE_THRESHOLD = 0.7  # Minimum confidence for positive detection
BATCH_SIZE = 8  # Number of frames to process in each batch

# Shoplifting Detection Prompts
SHOPLIFTING_PROMPTS = [
    "a person stealing merchandise from a store",
    "someone hiding items in their bag or clothing",
    "a person concealing products without paying",
    "suspicious behavior in a retail store",
    "someone putting items in their pocket secretly",
    "a person looking around nervously while taking items",
    "theft in a store",
    "shoplifting activity",
    "someone taking items without permission",
    "concealing merchandise in clothing"
]

NORMAL_BEHAVIOR_PROMPTS = [
    "a person shopping normally in a store",
    "someone browsing products casually",
    "a customer examining merchandise",
    "normal shopping behavior",
    "a person walking through a store",
    "someone looking at products on shelves",
    "casual shopping activity",
    "a customer in a retail environment",
    "normal store interaction",
    "regular shopping behavior"
]

# Output Configuration
OUTPUT_DIR = "detection_results"
SAVE_DETECTED_FRAMES = True
DETECTION_LOG_FILE = "detection_log.txt"

# Video File Extensions
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
