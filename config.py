"""
Configuration file for the CLIP-based shoplifting detection system.
"""

import os

# Model Configuration
CLIP_MODEL_NAME = "zer0int/CLIP-GmP-ViT-L-14"
DEVICE = "cuda" if os.environ.get("CUDA_AVAILABLE", "false").lower() == "true" else "cpu"

# Video Processing Configuration
FRAME_EXTRACTION_RATE = 1  # Extract every N frames (1 = every frame)
MAX_FRAMES_PER_VIDEO = 300  # Maximum frames to process per video
FRAME_RESIZE_WIDTH = 224
FRAME_RESIZE_HEIGHT = 224

# Detection Configuration - Multi-level thresholds for better accuracy
CONFIDENCE_THRESHOLD_HIGH = 0.35   # High confidence threshold for HIGH risk
CONFIDENCE_THRESHOLD_MEDIUM = 0.25 # Medium confidence threshold for MEDIUM risk
CONFIDENCE_THRESHOLD_LOW = 0.15    # Low confidence threshold for LOW risk
BATCH_SIZE = 8  # Number of frames to process in each batch

# Legacy threshold for backward compatibility
CONFIDENCE_THRESHOLD = CONFIDENCE_THRESHOLD_MEDIUM

# Shoplifting Detection Prompts - Enhanced for better detection
SHOPLIFTING_PROMPTS = [
    "a person stealing merchandise from a store",
    "someone hiding items in their bag or clothing",
    "a person concealing products without paying",
    "suspicious behavior in a retail store",
    "someone putting items in their pocket secretly",
    "a person looking around nervously while taking items",
    "theft in a store",
    "shoplifting activity",
    "someone taking items without permission",
    "concealing merchandise in clothing",
    "a person stuffing items into a bag",
    "someone quickly grabbing items and hiding them",
    "suspicious movements near store merchandise",
    "a person acting nervously around products",
    "someone checking if they're being watched while taking items"
]

NORMAL_BEHAVIOR_PROMPTS = [
    "a person shopping normally in a store",
    "someone browsing products casually",
    "a customer examining merchandise",
    "normal shopping behavior",
    "a person walking through a store",
    "someone looking at products on shelves",
    "casual shopping activity",
    "a customer in a retail environment",
    "normal store interaction",
    "regular shopping behavior"
]

# Output Configuration
OUTPUT_DIR = "detection_results"
SAVE_DETECTED_FRAMES = True
DETECTION_LOG_FILE = "detection_log.txt"

# Video File Extensions
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
