"""
Test script to verify improved detection with lower threshold.
"""

import os
import json
from shoplifting_detector import ShopliftingDetectionSystem

def test_improved_detection():
    """Test a few shoplifting videos with the new threshold."""
    
    # Test videos from the shoplifting dataset
    test_videos = [
        r"c:\Users\<USER>\Desktop\video pour mohamed\video vol\Magasin_general_286_2025_05_09_17_35_21_mY7NjOcW_shoplifting_dif.mp4",
        r"c:\Users\<USER>\Desktop\video pour mohamed\video vol\Magasin_general_286_2025_06_19_11_44_38_0B7xZpMm_shoplifting_def.mp4",
        r"c:\Users\<USER>\Desktop\video pour mohamed\video vol\Monoprix_AIN_ZAGHOUAN_08_304_2025_06_11_14_05_07_QCllyQtA_shopli.mp4"
    ]
    
    # Test a normal video for comparison
    normal_video = r"c:\Users\<USER>\Desktop\video pour mohamed\video normale\video1.mp4"
    
    print("="*60)
    print("TESTING IMPROVED DETECTION WITH LOWER THRESHOLD (0.2)")
    print("="*60)
    
    detector = ShopliftingDetectionSystem()
    
    print("\n🔍 Testing SHOPLIFTING videos:")
    print("-" * 40)
    
    for i, video_path in enumerate(test_videos, 1):
        if os.path.exists(video_path):
            video_name = os.path.basename(video_path)
            print(f"\n{i}. {video_name}")
            
            try:
                results = detector.process_video_keyframes(video_path, num_keyframes=10)
                analysis = results['analysis']
                
                print(f"   Risk Level: {analysis['risk_level']}")
                print(f"   Suspicion Rate: {analysis['suspicion_rate']:.2%}")
                print(f"   Suspicious Frames: {analysis['suspicious_frames']}/{analysis['total_frames']}")
                print(f"   Max Confidence: {analysis['max_confidence']:.3f}")
                print(f"   Avg Confidence: {analysis['average_confidence']:.3f}")
                
                if analysis['suspicious_frames'] > 0:
                    print(f"   ✅ DETECTION IMPROVED!")
                else:
                    print(f"   ❌ Still no detection")
                    
            except Exception as e:
                print(f"   Error: {e}")
        else:
            print(f"{i}. Video not found: {os.path.basename(video_path)}")
    
    print(f"\n🛒 Testing NORMAL video for comparison:")
    print("-" * 40)
    
    if os.path.exists(normal_video):
        video_name = os.path.basename(normal_video)
        print(f"\n{video_name}")
        
        try:
            results = detector.process_video_keyframes(normal_video, num_keyframes=10)
            analysis = results['analysis']
            
            print(f"   Risk Level: {analysis['risk_level']}")
            print(f"   Suspicion Rate: {analysis['suspicion_rate']:.2%}")
            print(f"   Suspicious Frames: {analysis['suspicious_frames']}/{analysis['total_frames']}")
            print(f"   Max Confidence: {analysis['max_confidence']:.3f}")
            print(f"   Avg Confidence: {analysis['average_confidence']:.3f}")
            
            if analysis['suspicious_frames'] == 0:
                print(f"   ✅ Correctly identified as normal")
            else:
                print(f"   ⚠️  False positive detected")
                
        except Exception as e:
            print(f"   Error: {e}")
    else:
        print("Normal video not found")
    
    print(f"\n{'='*60}")
    print("RECOMMENDATIONS:")
    print("="*60)
    print("1. If shoplifting videos now show detections, the threshold was the issue")
    print("2. If normal videos show false positives, we may need to fine-tune prompts")
    print("3. Consider using different thresholds for different risk levels")
    print("4. May need to implement temporal analysis for better accuracy")


if __name__ == "__main__":
    test_improved_detection()
