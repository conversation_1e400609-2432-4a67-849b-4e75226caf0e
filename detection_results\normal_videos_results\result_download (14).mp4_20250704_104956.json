{"video_path": "c:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale\\download (14).mp4", "video_info": {"fps": 15.0, "frame_count": 288, "width": 704, "height": 576, "duration_seconds": 19.2, "file_size_mb": 1.1005840301513672}, "processing_info": {"total_keyframes_processed": 15, "keyframe_mode": true, "processing_timestamp": "2025-07-04T10:49:56.698075"}, "detections": [{"image_index": 0, "is_shoplifting": false, "confidence": -0.21916529536247253, "shoplifting_probability": 0.4147215783596039, "normal_probability": 0.6338868737220764, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "a person walking through a store", "frame_number": 0, "timestamp": 0.0}, {"image_index": 1, "is_shoplifting": false, "confidence": 0.12585985660552979, "shoplifting_probability": 0.6693412661552429, "normal_probability": 0.5434814095497131, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "a person walking through a store", "frame_number": 19, "timestamp": 1.2666666666666666}, {"image_index": 2, "is_shoplifting": false, "confidence": 0.22984182834625244, "shoplifting_probability": 0.7207689881324768, "normal_probability": 0.49092715978622437, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "a person walking through a store", "frame_number": 38, "timestamp": 2.533333333333333}, {"image_index": 3, "is_shoplifting": false, "confidence": 0.005468517541885376, "shoplifting_probability": 0.44772136211395264, "normal_probability": 0.44225284457206726, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 57, "timestamp": 3.8}, {"image_index": 4, "is_shoplifting": false, "confidence": -0.02407941222190857, "shoplifting_probability": 0.416257381439209, "normal_probability": 0.44033679366111755, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 76, "timestamp": 5.066666666666666}, {"image_index": 5, "is_shoplifting": false, "confidence": 0.16418376564979553, "shoplifting_probability": 0.4861946403980255, "normal_probability": 0.32201087474823, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 95, "timestamp": 6.333333333333333}, {"image_index": 6, "is_shoplifting": false, "confidence": 0.224028080701828, "shoplifting_probability": 0.5524939298629761, "normal_probability": 0.32846584916114807, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 114, "timestamp": 7.6}, {"image_index": 7, "is_shoplifting": false, "confidence": 0.2855522036552429, "shoplifting_probability": 0.5706593990325928, "normal_probability": 0.28510719537734985, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 133, "timestamp": 8.866666666666667}, {"image_index": 8, "is_shoplifting": false, "confidence": 0.03932487964630127, "shoplifting_probability": 0.3792363107204437, "normal_probability": 0.33991143107414246, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "a person walking through a store", "frame_number": 152, "timestamp": 10.133333333333333}, {"image_index": 9, "is_shoplifting": false, "confidence": 0.09551829099655151, "shoplifting_probability": 0.49773934483528137, "normal_probability": 0.40222105383872986, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 171, "timestamp": 11.4}, {"image_index": 10, "is_shoplifting": false, "confidence": 0.19728365540504456, "shoplifting_probability": 0.5214406847953796, "normal_probability": 0.3241570293903351, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 190, "timestamp": 12.666666666666666}, {"image_index": 11, "is_shoplifting": false, "confidence": -0.03471112251281738, "shoplifting_probability": 0.610686182975769, "normal_probability": 0.6453973054885864, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "a person walking through a store", "frame_number": 209, "timestamp": 13.933333333333334}, {"image_index": 12, "is_shoplifting": false, "confidence": -0.01512250304222107, "shoplifting_probability": 0.43635839223861694, "normal_probability": 0.451480895280838, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "a person walking through a store", "frame_number": 228, "timestamp": 15.2}, {"image_index": 13, "is_shoplifting": false, "confidence": 0.08921557664871216, "shoplifting_probability": 0.40603238344192505, "normal_probability": 0.3168168067932129, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 247, "timestamp": 16.466666666666665}, {"image_index": 14, "is_shoplifting": false, "confidence": 0.30276623368263245, "shoplifting_probability": 0.6584143042564392, "normal_probability": 0.35564807057380676, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 266, "timestamp": 17.733333333333334}], "analysis": {"total_frames": 15, "suspicious_frames": 0, "suspicion_rate": 0.0, "average_confidence": 0.09773097038269044, "max_confidence": 0.30276623368263245, "min_confidence": -0.21916529536247253, "suspicious_periods": [], "risk_level": "NONE"}, "saved_frames": []}