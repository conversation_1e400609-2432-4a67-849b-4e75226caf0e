"""
Example usage scripts for the CLIP-based shoplifting detection system.
"""

import os
import sys
import logging
from PIL import Image
import numpy as np

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from clip_model import CLIPShopliftingDetector
from video_processor import VideoProcessor
from shoplifting_detector import ShopliftingDetectionSystem
from utils import create_detection_timeline, create_detection_report
import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_single_image_detection():
    """
    Example: Detect shoplifting in a single image.
    """
    print("="*60)
    print("EXAMPLE: Single Image Detection")
    print("="*60)
    
    # Initialize the CLIP detector
    detector = CLIPShopliftingDetector()
    
    # Create a dummy image for demonstration (you would load a real image)
    # For this example, we'll create a simple test image
    dummy_image = Image.new('RGB', (224, 224), color='white')
    
    # Detect shoplifting in the image
    result = detector.detect_single_image(dummy_image)
    
    print(f"Is shoplifting detected: {result['is_shoplifting']}")
    print(f"Confidence: {result['confidence']:.3f}")
    print(f"Shoplifting probability: {result['shoplifting_probability']:.3f}")
    print(f"Normal probability: {result['normal_probability']:.3f}")
    print(f"Best matching shoplifting prompt: {result['best_shoplifting_prompt']}")
    print(f"Best matching normal prompt: {result['best_normal_prompt']}")


def example_video_processing_info():
    """
    Example: Get video information without processing.
    """
    print("\n" + "="*60)
    print("EXAMPLE: Video Information")
    print("="*60)
    
    # You would replace this with an actual video file path
    video_path = "example_video.mp4"
    
    if not os.path.exists(video_path):
        print(f"Note: Video file '{video_path}' not found.")
        print("This is just a demonstration of how to get video info.")
        return
    
    processor = VideoProcessor()
    
    try:
        video_info = processor.get_video_info(video_path)
        
        print(f"Video file: {video_path}")
        print(f"Duration: {video_info['duration_seconds']:.2f} seconds")
        print(f"FPS: {video_info['fps']:.2f}")
        print(f"Resolution: {video_info['width']}x{video_info['height']}")
        print(f"Total frames: {video_info['frame_count']}")
        print(f"File size: {video_info['file_size_mb']:.2f} MB")
        
    except Exception as e:
        print(f"Error processing video: {e}")


def example_batch_detection():
    """
    Example: Detect shoplifting in multiple images.
    """
    print("\n" + "="*60)
    print("EXAMPLE: Batch Image Detection")
    print("="*60)
    
    # Initialize the CLIP detector
    detector = CLIPShopliftingDetector()
    
    # Create dummy images for demonstration
    images = []
    for i in range(3):
        # Create different colored images to simulate different scenes
        color = (i * 80, (i * 60) % 255, (i * 40) % 255)
        dummy_image = Image.new('RGB', (224, 224), color=color)
        images.append(dummy_image)
    
    # Detect shoplifting in batch
    results = detector.detect_shoplifting(images)
    
    print(f"Processed {len(images)} images:")
    for i, result in enumerate(results):
        print(f"\nImage {i+1}:")
        print(f"  Is shoplifting: {result['is_shoplifting']}")
        print(f"  Confidence: {result['confidence']:.3f}")
        print(f"  Shoplifting prob: {result['shoplifting_probability']:.3f}")


def example_full_video_detection():
    """
    Example: Complete video detection workflow.
    """
    print("\n" + "="*60)
    print("EXAMPLE: Full Video Detection")
    print("="*60)
    
    # You would replace this with an actual video file path
    video_path = "example_video.mp4"
    
    if not os.path.exists(video_path):
        print(f"Note: Video file '{video_path}' not found.")
        print("To run this example, place a video file named 'example_video.mp4' in the current directory.")
        print("Supported formats: .mp4, .avi, .mov, .mkv, .wmv, .flv")
        return
    
    # Initialize the detection system
    detection_system = ShopliftingDetectionSystem()
    
    try:
        # Process the video (using keyframe mode for faster processing)
        print("Processing video in keyframe mode...")
        results = detection_system.process_video_keyframes(video_path, num_keyframes=10)
        
        # Print summary
        analysis = results['analysis']
        print(f"\nDetection Results:")
        print(f"Total frames processed: {analysis['total_frames']}")
        print(f"Suspicious frames: {analysis['suspicious_frames']}")
        print(f"Suspicion rate: {analysis['suspicion_rate']:.2%}")
        print(f"Risk level: {analysis['risk_level']}")
        print(f"Max confidence: {analysis['max_confidence']:.3f}")
        
        if analysis['suspicious_periods']:
            print(f"\nSuspicious periods: {len(analysis['suspicious_periods'])}")
            for i, period in enumerate(analysis['suspicious_periods'], 1):
                print(f"  Period {i}: {period['start_time']:.2f}s - {period['end_time']:.2f}s")
        
        # Save results
        output_file = detection_system.save_results(results)
        print(f"\nResults saved to: {output_file}")
        
        # Create visualizations
        timeline_path = create_detection_timeline(results)
        if timeline_path:
            print(f"Timeline visualization saved to: {timeline_path}")
        
        report_path = create_detection_report(results)
        print(f"HTML report saved to: {report_path}")
        
    except Exception as e:
        print(f"Error processing video: {e}")
        import traceback
        traceback.print_exc()


def example_custom_prompts():
    """
    Example: Using custom detection prompts.
    """
    print("\n" + "="*60)
    print("EXAMPLE: Custom Detection Prompts")
    print("="*60)
    
    # Show current prompts
    print("Current shoplifting prompts:")
    for i, prompt in enumerate(config.SHOPLIFTING_PROMPTS[:3], 1):
        print(f"  {i}. {prompt}")
    print("  ...")
    
    print("\nCurrent normal behavior prompts:")
    for i, prompt in enumerate(config.NORMAL_BEHAVIOR_PROMPTS[:3], 1):
        print(f"  {i}. {prompt}")
    print("  ...")
    
    print("\nYou can modify these prompts in config.py to customize detection behavior.")
    print("For example, you could add prompts specific to your store layout or products.")


def example_configuration():
    """
    Example: Show current configuration settings.
    """
    print("\n" + "="*60)
    print("EXAMPLE: Current Configuration")
    print("="*60)
    
    print(f"CLIP Model: {config.CLIP_MODEL_NAME}")
    print(f"Device: {config.DEVICE}")
    print(f"Frame extraction rate: {config.FRAME_EXTRACTION_RATE}")
    print(f"Max frames per video: {config.MAX_FRAMES_PER_VIDEO}")
    print(f"Frame resize: {config.FRAME_RESIZE_WIDTH}x{config.FRAME_RESIZE_HEIGHT}")
    print(f"Confidence threshold: {config.CONFIDENCE_THRESHOLD}")
    print(f"Batch size: {config.BATCH_SIZE}")
    print(f"Output directory: {config.OUTPUT_DIR}")
    print(f"Save detected frames: {config.SAVE_DETECTED_FRAMES}")
    
    print("\nYou can modify these settings in config.py to customize the system behavior.")


def main():
    """
    Run all examples.
    """
    print("CLIP-based Shoplifting Detection System - Examples")
    print("This script demonstrates various features of the detection system.")
    print("\nNote: Some examples require actual video files to work properly.")
    
    try:
        # Run examples that don't require external files
        example_single_image_detection()
        example_batch_detection()
        example_custom_prompts()
        example_configuration()
        
        # Run examples that might require external files
        example_video_processing_info()
        example_full_video_detection()
        
        print("\n" + "="*60)
        print("Examples completed!")
        print("="*60)
        print("\nTo use the system with your own videos:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run: python main.py your_video.mp4")
        print("3. Check the detection_results/ directory for outputs")
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
