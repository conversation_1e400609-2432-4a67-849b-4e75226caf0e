"""
Test script to verify the shoplifting detection system installation.
"""

import sys
import os
import logging
from PIL import Image
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        import torchvision
        print(f"✓ TorchVision {torchvision.__version__}")
    except ImportError as e:
        print(f"✗ TorchVision import failed: {e}")
        return False
    
    try:
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
    except ImportError as e:
        print(f"✗ Transformers import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow")
    except ImportError as e:
        print(f"✗ Pillow import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    try:
        import matplotlib
        print(f"✓ Matplotlib {matplotlib.__version__}")
    except ImportError as e:
        print(f"✗ Matplotlib import failed: {e}")
        return False
    
    return True


def test_local_modules():
    """Test if local modules can be imported."""
    print("\nTesting local modules...")
    
    try:
        import config
        print("✓ config.py")
    except ImportError as e:
        print(f"✗ config.py import failed: {e}")
        return False
    
    try:
        from clip_model import CLIPShopliftingDetector
        print("✓ clip_model.py")
    except ImportError as e:
        print(f"✗ clip_model.py import failed: {e}")
        return False
    
    try:
        from video_processor import VideoProcessor
        print("✓ video_processor.py")
    except ImportError as e:
        print(f"✗ video_processor.py import failed: {e}")
        return False
    
    try:
        from shoplifting_detector import ShopliftingDetectionSystem
        print("✓ shoplifting_detector.py")
    except ImportError as e:
        print(f"✗ shoplifting_detector.py import failed: {e}")
        return False
    
    try:
        import utils
        print("✓ utils.py")
    except ImportError as e:
        print(f"✗ utils.py import failed: {e}")
        return False
    
    return True


def test_model_loading():
    """Test if the CLIP model can be loaded."""
    print("\nTesting CLIP model loading...")
    
    try:
        from clip_model import CLIPShopliftingDetector
        import config
        
        print(f"Attempting to load model: {config.CLIP_MODEL_NAME}")
        print("This may take a few minutes on first run...")
        
        # Try to initialize the detector
        detector = CLIPShopliftingDetector()
        print("✓ CLIP model loaded successfully")
        
        # Test with a dummy image
        dummy_image = Image.new('RGB', (224, 224), color='white')
        result = detector.detect_single_image(dummy_image)
        
        print("✓ Model inference test passed")
        print(f"  Sample result: confidence={result['confidence']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        print("This might be due to:")
        print("  - Internet connection issues (model needs to be downloaded)")
        print("  - Insufficient memory")
        print("  - CUDA/GPU configuration issues")
        return False


def test_video_processor():
    """Test video processor functionality."""
    print("\nTesting video processor...")
    
    try:
        from video_processor import VideoProcessor
        
        processor = VideoProcessor()
        print("✓ VideoProcessor initialized")
        
        # Test supported format checking
        test_files = ["test.mp4", "test.avi", "test.mov", "test.xyz"]
        for file in test_files:
            is_supported = processor.is_supported_format(file)
            expected = file.endswith(('.mp4', '.avi', '.mov'))
            if is_supported == expected:
                print(f"✓ Format check for {file}: {is_supported}")
            else:
                print(f"✗ Format check failed for {file}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Video processor test failed: {e}")
        return False


def test_configuration():
    """Test configuration settings."""
    print("\nTesting configuration...")
    
    try:
        import config
        
        # Check required configuration variables
        required_vars = [
            'CLIP_MODEL_NAME', 'DEVICE', 'FRAME_EXTRACTION_RATE',
            'MAX_FRAMES_PER_VIDEO', 'CONFIDENCE_THRESHOLD', 'BATCH_SIZE',
            'SHOPLIFTING_PROMPTS', 'NORMAL_BEHAVIOR_PROMPTS', 'OUTPUT_DIR'
        ]
        
        for var in required_vars:
            if hasattr(config, var):
                value = getattr(config, var)
                print(f"✓ {var}: {type(value).__name__}")
            else:
                print(f"✗ Missing configuration variable: {var}")
                return False
        
        # Check if prompts are non-empty
        if len(config.SHOPLIFTING_PROMPTS) > 0:
            print(f"✓ Shoplifting prompts: {len(config.SHOPLIFTING_PROMPTS)} items")
        else:
            print("✗ No shoplifting prompts defined")
            return False
        
        if len(config.NORMAL_BEHAVIOR_PROMPTS) > 0:
            print(f"✓ Normal behavior prompts: {len(config.NORMAL_BEHAVIOR_PROMPTS)} items")
        else:
            print("✗ No normal behavior prompts defined")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_output_directory():
    """Test output directory creation."""
    print("\nTesting output directory...")
    
    try:
        import config
        
        # Create output directory if it doesn't exist
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
        if os.path.exists(config.OUTPUT_DIR):
            print(f"✓ Output directory exists: {config.OUTPUT_DIR}")
        else:
            print(f"✗ Failed to create output directory: {config.OUTPUT_DIR}")
            return False
        
        # Test write permissions
        test_file = os.path.join(config.OUTPUT_DIR, "test_write.txt")
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("✓ Output directory is writable")
        except Exception as e:
            print(f"✗ Output directory is not writable: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Output directory test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("="*60)
    print("SHOPLIFTING DETECTION SYSTEM - INSTALLATION TEST")
    print("="*60)
    
    tests = [
        ("Dependencies", test_imports),
        ("Local Modules", test_local_modules),
        ("Configuration", test_configuration),
        ("Output Directory", test_output_directory),
        ("Video Processor", test_video_processor),
        ("CLIP Model", test_model_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-'*40}")
        print(f"Running {test_name} Test")
        print(f"{'-'*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Run 'python example_usage.py' to see usage examples")
        print("2. Run 'python main.py your_video.mp4' to analyze a video")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("- Run 'pip install -r requirements.txt' to install dependencies")
        print("- Check your internet connection for model downloading")
        print("- Ensure you have sufficient disk space and memory")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
