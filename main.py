"""
Main Application for CLIP-based Shoplifting Detection System
"""

import argparse
import os
import sys
import logging
from typing import List
import json

from shoplifting_detector import ShopliftingDetectionSystem
import config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.getLogger().setLevel(level)


def print_results_summary(results: dict):
    """Print a summary of detection results."""
    analysis = results['analysis']
    video_info = results['video_info']
    
    print("\n" + "="*60)
    print("SHOPLIFTING DETECTION RESULTS")
    print("="*60)
    print(f"Video: {os.path.basename(results['video_path'])}")
    print(f"Duration: {video_info['duration_seconds']:.2f} seconds")
    print(f"Total frames processed: {analysis['total_frames']}")
    print(f"Suspicious frames detected: {analysis['suspicious_frames']}")
    print(f"Suspicion rate: {analysis['suspicion_rate']:.2%}")
    print(f"Risk level: {analysis['risk_level']}")
    print(f"Max confidence: {analysis['max_confidence']:.3f}")
    print(f"Average confidence: {analysis['average_confidence']:.3f}")
    
    if analysis['suspicious_periods']:
        print(f"\nSuspicious periods detected: {len(analysis['suspicious_periods'])}")
        for i, period in enumerate(analysis['suspicious_periods'], 1):
            print(f"  Period {i}: {period['start_time']:.2f}s - {period['end_time']:.2f}s "
                  f"({period['duration']:.2f}s, {period['frame_count']} frames, "
                  f"max confidence: {period['max_confidence']:.3f})")
    
    if results['saved_frames']:
        print(f"\nSuspicious frames saved: {len(results['saved_frames'])}")
        print(f"Output directory: {os.path.dirname(results['saved_frames'][0])}")
    
    print("="*60)


def process_single_video(video_path: str, args):
    """Process a single video file."""
    if not os.path.exists(video_path):
        logger.error(f"Video file not found: {video_path}")
        return None
    
    logger.info(f"Initializing detection system...")
    detector = ShopliftingDetectionSystem(
        model_name=args.model_name,
        device=args.device
    )
    
    try:
        if args.keyframes_only:
            logger.info(f"Processing video in keyframe mode: {video_path}")
            results = detector.process_video_keyframes(
                video_path, 
                num_keyframes=args.num_keyframes
            )
        else:
            logger.info(f"Processing video in full mode: {video_path}")
            results = detector.process_video_batch(
                video_path,
                batch_size=args.batch_size,
                start_time=args.start_time,
                end_time=args.end_time
            )
        
        # Save results
        output_file = detector.save_results(results, args.output_file)
        
        # Print summary
        if not args.quiet:
            print_results_summary(results)
        
        return results
        
    except Exception as e:
        logger.error(f"Error processing video {video_path}: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return None


def process_multiple_videos(video_paths: List[str], args):
    """Process multiple video files."""
    all_results = []
    
    for i, video_path in enumerate(video_paths, 1):
        logger.info(f"Processing video {i}/{len(video_paths)}: {video_path}")
        
        results = process_single_video(video_path, args)
        if results:
            all_results.append(results)
    
    # Create summary report
    if all_results and not args.quiet:
        print("\n" + "="*60)
        print("BATCH PROCESSING SUMMARY")
        print("="*60)
        
        total_videos = len(all_results)
        high_risk_videos = sum(1 for r in all_results if r['analysis']['risk_level'] == 'HIGH')
        medium_risk_videos = sum(1 for r in all_results if r['analysis']['risk_level'] == 'MEDIUM')
        
        print(f"Total videos processed: {total_videos}")
        print(f"High risk videos: {high_risk_videos}")
        print(f"Medium risk videos: {medium_risk_videos}")
        print(f"Low/No risk videos: {total_videos - high_risk_videos - medium_risk_videos}")
        
        if high_risk_videos > 0:
            print("\nHigh risk videos:")
            for result in all_results:
                if result['analysis']['risk_level'] == 'HIGH':
                    video_name = os.path.basename(result['video_path'])
                    suspicion_rate = result['analysis']['suspicion_rate']
                    print(f"  - {video_name} (suspicion rate: {suspicion_rate:.2%})")
        
        print("="*60)
    
    return all_results


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="CLIP-based Shoplifting Detection System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process a single video
  python main.py video.mp4
  
  # Process multiple videos
  python main.py video1.mp4 video2.mp4 video3.mp4
  
  # Process with keyframes only (faster)
  python main.py --keyframes-only video.mp4
  
  # Process specific time range
  python main.py --start-time 30 --end-time 120 video.mp4
  
  # Use custom model and device
  python main.py --model-name "custom/clip-model" --device cuda video.mp4
        """
    )
    
    # Required arguments
    parser.add_argument('videos', nargs='+', help='Video file(s) to process')
    
    # Processing options
    parser.add_argument('--keyframes-only', action='store_true',
                       help='Process only key frames for faster analysis')
    parser.add_argument('--num-keyframes', type=int, default=20,
                       help='Number of key frames to extract (default: 20)')
    parser.add_argument('--batch-size', type=int, default=config.BATCH_SIZE,
                       help=f'Batch size for processing (default: {config.BATCH_SIZE})')
    parser.add_argument('--start-time', type=float, default=0,
                       help='Start time in seconds (default: 0)')
    parser.add_argument('--end-time', type=float, default=None,
                       help='End time in seconds (default: entire video)')
    
    # Model options
    parser.add_argument('--model-name', type=str, default=config.CLIP_MODEL_NAME,
                       help=f'CLIP model name (default: {config.CLIP_MODEL_NAME})')
    parser.add_argument('--device', type=str, default=config.DEVICE,
                       help=f'Device to use (default: {config.DEVICE})')
    
    # Output options
    parser.add_argument('--output-file', type=str, default=None,
                       help='Output file for results (default: auto-generated)')
    parser.add_argument('--output-dir', type=str, default=config.OUTPUT_DIR,
                       help=f'Output directory (default: {config.OUTPUT_DIR})')
    
    # Logging options
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Suppress output summaries')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Update config with command line arguments
    if args.output_dir != config.OUTPUT_DIR:
        config.OUTPUT_DIR = args.output_dir
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # Validate video files
    valid_videos = []
    for video_path in args.videos:
        if os.path.exists(video_path):
            valid_videos.append(video_path)
        else:
            logger.warning(f"Video file not found: {video_path}")
    
    if not valid_videos:
        logger.error("No valid video files found")
        sys.exit(1)
    
    # Process videos
    try:
        if len(valid_videos) == 1:
            results = process_single_video(valid_videos[0], args)
            if results is None:
                sys.exit(1)
        else:
            results = process_multiple_videos(valid_videos, args)
            if not results:
                sys.exit(1)
        
        logger.info("Processing completed successfully")
        
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
