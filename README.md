# CLIP-based Shoplifting Detection System

A comprehensive video analysis system that uses the CLIP-GmP-ViT-L-14 model to detect potential shoplifting behavior in retail surveillance videos.

## 🚀 **Overview**

This system leverages the power of OpenAI's CLIP (Contrastive Language-Image Pretraining) model to analyze video footage and identify suspicious activities that may indicate shoplifting. The system processes video frames and compares them against textual descriptions of both normal shopping behavior and suspicious activities.

### **Key Features**:
- **Real-time Video Analysis**: Process video files to detect suspicious behavior frame by frame
- **Batch Processing**: Analyze multiple videos efficiently with configurable batch sizes
- **Keyframe Analysis**: Fast analysis mode using representative frames from videos
- **Confidence Scoring**: Provides confidence levels for each detection
- **Timeline Visualization**: Generate visual timelines showing suspicious periods
- **Detailed Reporting**: HTML reports with comprehensive analysis results
- **Flexible Configuration**: Customizable detection prompts and thresholds

## 🛠️ **System Architecture**

The system consists of several key components:

### **1. CLIP Model Wrapper (`clip_model.py`)**:
- Loads and manages the CLIP-GmP-ViT-L-14 model
- Handles image preprocessing and text encoding
- Computes similarity scores between video frames and behavior descriptions
- Provides confidence-based detection results

### **2. Video Processor (`video_processor.py`)**:
- Extracts frames from video files at configurable intervals
- Handles multiple video formats (MP4, AVI, MOV, MKV, WMV, FLV)
- Resizes and preprocesses frames for optimal model performance
- Supports time-range processing and keyframe extraction

### **3. Detection System (`shoplifting_detector.py`)**:
- Orchestrates the complete detection pipeline
- Processes videos in batches for memory efficiency
- Analyzes detection patterns and identifies suspicious periods
- Generates comprehensive results with statistical analysis

### **4. Main Application (`main.py`)**:
- Command-line interface for easy usage
- Supports single video and batch processing
- Configurable processing options and output formats
- Progress tracking and error handling

## 📋 **Requirements**

- **Python**: 3.8 or higher
- **Dependencies**:
  - `torch>=2.0.0`
  - `torchvision>=0.15.0`
  - `transformers>=4.30.0`
  - `opencv-python>=4.8.0`
  - `pillow>=9.5.0`
  - `numpy>=1.24.0`
  - `matplotlib>=3.7.0`
  - `tqdm>=4.65.0`
  - `scikit-learn>=1.3.0`
  - `pandas>=2.0.0`

## 🚀 **Installation and Setup**

1. **Clone the Repository**:
   ```bash
   git clone <repository-url>
   cd Modelo-CLIP
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the System** (optional):
   Edit `config.py` to customize detection prompts, thresholds, and processing parameters.

## 💻 **Usage**

### **Basic Usage**

Process a single video:
```bash
python main.py video.mp4
```

Process multiple videos:
```bash
python main.py video1.mp4 video2.mp4 video3.mp4
```

### **Advanced Options**

Fast analysis using keyframes only:
```bash
python main.py --keyframes-only --num-keyframes 20 video.mp4
```

Process specific time range:
```bash
python main.py --start-time 30 --end-time 120 video.mp4
```

Custom batch size and device:
```bash
python main.py --batch-size 16 --device cuda video.mp4
```

Quiet mode (suppress output summaries):
```bash
python main.py --quiet video.mp4
```

### **Command Line Options**

- `--keyframes-only`: Process only key frames for faster analysis
- `--num-keyframes N`: Number of key frames to extract (default: 20)
- `--batch-size N`: Batch size for processing (default: 8)
- `--start-time T`: Start time in seconds (default: 0)
- `--end-time T`: End time in seconds (default: entire video)
- `--model-name MODEL`: CLIP model name (default: zer0int/CLIP-GmP-ViT-L-14)
- `--device DEVICE`: Device to use (cpu/cuda, default: auto-detect)
- `--output-file FILE`: Output file for results
- `--output-dir DIR`: Output directory (default: detection_results)
- `--verbose, -v`: Enable verbose logging
- `--quiet, -q`: Suppress output summaries

## 📊 **Output and Results**

The system generates several types of output:

### **1. Detection Results (JSON)**
Comprehensive results including:
- Video information (duration, resolution, FPS)
- Frame-by-frame detection results
- Statistical analysis and risk assessment
- Suspicious periods identification

### **2. Visual Timeline**
PNG image showing:
- Confidence scores over time
- Suspicious periods highlighted
- Detection threshold visualization

### **3. HTML Report**
Detailed report including:
- Video metadata and processing information
- Detection summary with risk levels
- Suspicious periods table
- Visual charts and statistics

### **4. Suspicious Frames**
Individual frame images saved for detected suspicious activities with confidence scores.

## ⚙️ **Configuration**

The system can be customized through `config.py`:

### **Detection Prompts**
Modify `SHOPLIFTING_PROMPTS` and `NORMAL_BEHAVIOR_PROMPTS` to customize what the system looks for:

```python
SHOPLIFTING_PROMPTS = [
    "a person stealing merchandise from a store",
    "someone hiding items in their bag or clothing",
    # Add your custom prompts here
]
```

### **Processing Parameters**
- `FRAME_EXTRACTION_RATE`: Extract every N frames
- `MAX_FRAMES_PER_VIDEO`: Maximum frames to process
- `CONFIDENCE_THRESHOLD`: Minimum confidence for detection
- `BATCH_SIZE`: Processing batch size

## 🔍 **Detection Logic**

The system works by:

1. **Frame Extraction**: Extracting frames from video at specified intervals
2. **Preprocessing**: Resizing and normalizing frames for CLIP input
3. **Similarity Computation**: Computing similarity between frames and behavior descriptions
4. **Confidence Calculation**: Determining confidence based on shoplifting vs. normal behavior scores
5. **Temporal Analysis**: Identifying patterns and suspicious periods
6. **Risk Assessment**: Categorizing overall risk level (NONE/LOW/MEDIUM/HIGH)

## 📈 **Performance Considerations**

- **GPU Acceleration**: Use CUDA-enabled GPU for faster processing
- **Batch Processing**: Larger batch sizes improve GPU utilization
- **Keyframe Mode**: Use for quick analysis of long videos
- **Memory Management**: System automatically handles memory-efficient processing

## 🔧 **Troubleshooting**

### **Common Issues**

1. **CUDA Out of Memory**: Reduce batch size or use CPU
2. **Unsupported Video Format**: Convert to supported format (MP4, AVI, etc.)
3. **Model Download Issues**: Ensure internet connection for first-time model download
4. **Permission Errors**: Check write permissions for output directory

### **Performance Tips**

- Use GPU when available for significant speedup
- Process shorter video segments for faster results
- Use keyframe mode for initial analysis
- Adjust frame extraction rate based on video content

## 🤝 **Contributing**

Contributions are welcome! Areas for improvement:
- Additional detection prompts for specific retail environments
- Support for real-time video streams
- Integration with existing surveillance systems
- Performance optimizations
- Additional output formats

## 📄 **License**

This project builds upon the CLIP-GmP-ViT-L-14 model. Please refer to the original model's license terms.

## 🔗 **References**

- [CLIP-GmP-ViT-L-14 on Hugging Face](https://huggingface.co/zer0int/CLIP-GmP-ViT-L-14)
- [Original CLIP Paper](https://arxiv.org/abs/2103.00020)
- [Vision Transformer Paper](https://arxiv.org/abs/2010.11929)
