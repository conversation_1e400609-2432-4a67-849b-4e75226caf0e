"""
CLIP Model Wrapper for Shoplifting Detection
"""

import torch
import numpy as np
from transformers import CLIPProcessor, CLIPModel
from PIL import Image
import logging
from typing import List, Tuple, Union
import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CLIPShopliftingDetector:
    """
    A wrapper class for the CLIP model specifically designed for shoplifting detection.
    """
    
    def __init__(self, model_name: str = None, device: str = None):
        """
        Initialize the CLIP model for shoplifting detection.
        
        Args:
            model_name (str): Name of the CLIP model to load
            device (str): Device to run the model on ('cpu' or 'cuda')
        """
        self.model_name = model_name or config.CLIP_MODEL_NAME
        self.device = device or config.DEVICE
        
        logger.info(f"Loading CLIP model: {self.model_name}")
        logger.info(f"Using device: {self.device}")
        
        try:
            # Load the model and processor
            self.model = CLIPModel.from_pretrained(self.model_name)
            self.processor = CLIPProcessor.from_pretrained(self.model_name)
            
            # Move model to specified device
            self.model.to(self.device)
            self.model.eval()
            
            logger.info("CLIP model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading CLIP model: {e}")
            raise
    
    def preprocess_image(self, image: Union[Image.Image, np.ndarray]) -> Image.Image:
        """
        Preprocess an image for CLIP model input.
        
        Args:
            image: PIL Image or numpy array
            
        Returns:
            PIL Image ready for processing
        """
        if isinstance(image, np.ndarray):
            image = Image.fromarray(image)
        
        # Ensure image is in RGB format
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        return image
    
    def compute_similarity(self, images: List[Union[Image.Image, np.ndarray]], 
                          texts: List[str]) -> torch.Tensor:
        """
        Compute similarity scores between images and texts.
        
        Args:
            images: List of PIL Images or numpy arrays
            texts: List of text descriptions
            
        Returns:
            Tensor of similarity scores with shape (len(images), len(texts))
        """
        # Preprocess images
        processed_images = [self.preprocess_image(img) for img in images]
        
        # Process inputs
        inputs = self.processor(
            text=texts,
            images=processed_images,
            return_tensors="pt",
            padding=True
        )
        
        # Move inputs to device
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Get model outputs
        with torch.no_grad():
            outputs = self.model(**inputs)
            
        # Get similarity scores
        logits_per_image = outputs.logits_per_image
        
        return logits_per_image
    
    def detect_shoplifting(self, images: List[Union[Image.Image, np.ndarray]], 
                          confidence_threshold: float = None) -> List[dict]:
        """
        Detect shoplifting behavior in a list of images.
        
        Args:
            images: List of images to analyze
            confidence_threshold: Minimum confidence for positive detection
            
        Returns:
            List of detection results for each image
        """
        if confidence_threshold is None:
            confidence_threshold = config.CONFIDENCE_THRESHOLD
            
        # Get similarity scores for shoplifting and normal behavior
        shoplifting_scores = self.compute_similarity(images, config.SHOPLIFTING_PROMPTS)
        normal_scores = self.compute_similarity(images, config.NORMAL_BEHAVIOR_PROMPTS)
        
        # Apply softmax to get probabilities
        shoplifting_probs = torch.softmax(shoplifting_scores, dim=1)
        normal_probs = torch.softmax(normal_scores, dim=1)
        
        # Get maximum probabilities for each category
        max_shoplifting_prob = torch.max(shoplifting_probs, dim=1)[0]
        max_normal_prob = torch.max(normal_probs, dim=1)[0]
        
        results = []
        for i, (shop_prob, norm_prob) in enumerate(zip(max_shoplifting_prob, max_normal_prob)):
            # Calculate confidence as the difference between shoplifting and normal behavior
            confidence = shop_prob.item() - norm_prob.item()
            
            # Determine if shoplifting is detected
            is_shoplifting = confidence > confidence_threshold
            
            # Get the best matching prompts
            best_shoplifting_idx = torch.argmax(shoplifting_probs[i]).item()
            best_normal_idx = torch.argmax(normal_probs[i]).item()
            
            result = {
                'image_index': i,
                'is_shoplifting': is_shoplifting,
                'confidence': confidence,
                'shoplifting_probability': shop_prob.item(),
                'normal_probability': norm_prob.item(),
                'best_shoplifting_prompt': config.SHOPLIFTING_PROMPTS[best_shoplifting_idx],
                'best_normal_prompt': config.NORMAL_BEHAVIOR_PROMPTS[best_normal_idx]
            }
            
            results.append(result)
            
        return results
    
    def detect_single_image(self, image: Union[Image.Image, np.ndarray], 
                           confidence_threshold: float = None) -> dict:
        """
        Detect shoplifting behavior in a single image.
        
        Args:
            image: Single image to analyze
            confidence_threshold: Minimum confidence for positive detection
            
        Returns:
            Detection result for the image
        """
        results = self.detect_shoplifting([image], confidence_threshold)
        return results[0]
