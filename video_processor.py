"""
Video Processing Pipeline for Shoplifting Detection
"""

import cv2
import numpy as np
from PIL import Image
import os
import logging
from typing import List, Tuple, Generator, Optional
from tqdm import tqdm
import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VideoProcessor:
    """
    Handles video file processing, frame extraction, and preprocessing.
    """
    
    def __init__(self, frame_rate: int = None, max_frames: int = None, 
                 resize_width: int = None, resize_height: int = None):
        """
        Initialize the video processor.
        
        Args:
            frame_rate: Extract every N frames (1 = every frame)
            max_frames: Maximum number of frames to extract
            resize_width: Target width for frame resizing
            resize_height: Target height for frame resizing
        """
        self.frame_rate = frame_rate or config.FRAME_EXTRACTION_RATE
        self.max_frames = max_frames or config.MAX_FRAMES_PER_VIDEO
        self.resize_width = resize_width or config.FRAME_RESIZE_WIDTH
        self.resize_height = resize_height or config.FRAME_RESIZE_HEIGHT
        
    def is_supported_format(self, video_path: str) -> bool:
        """
        Check if the video format is supported.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            True if format is supported, False otherwise
        """
        _, ext = os.path.splitext(video_path.lower())
        return ext in config.SUPPORTED_VIDEO_FORMATS
    
    def get_video_info(self, video_path: str) -> dict:
        """
        Get basic information about a video file.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Dictionary containing video information
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
            
        if not self.is_supported_format(video_path):
            raise ValueError(f"Unsupported video format: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        
        try:
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            info = {
                'fps': fps,
                'frame_count': frame_count,
                'width': width,
                'height': height,
                'duration_seconds': duration,
                'file_size_mb': os.path.getsize(video_path) / (1024 * 1024)
            }
            
            return info
            
        finally:
            cap.release()
    
    def extract_frames(self, video_path: str, start_time: float = 0, 
                      end_time: Optional[float] = None) -> Generator[Tuple[int, np.ndarray], None, None]:
        """
        Extract frames from a video file.
        
        Args:
            video_path: Path to the video file
            start_time: Start time in seconds
            end_time: End time in seconds (None for entire video)
            
        Yields:
            Tuple of (frame_number, frame_array)
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
            
        cap = cv2.VideoCapture(video_path)
        
        try:
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Calculate start and end frame numbers
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps) if end_time else total_frames
            end_frame = min(end_frame, total_frames)
            
            # Set starting position
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            frame_count = 0
            extracted_count = 0
            
            logger.info(f"Extracting frames from {video_path}")
            logger.info(f"Frame range: {start_frame} to {end_frame}")
            
            with tqdm(total=min(self.max_frames, (end_frame - start_frame) // self.frame_rate)) as pbar:
                while True:
                    ret, frame = cap.read()
                    
                    if not ret or frame_count + start_frame >= end_frame:
                        break
                    
                    # Extract frame at specified rate
                    if frame_count % self.frame_rate == 0:
                        # Resize frame
                        resized_frame = cv2.resize(frame, (self.resize_width, self.resize_height))
                        
                        # Convert BGR to RGB
                        rgb_frame = cv2.cvtColor(resized_frame, cv2.COLOR_BGR2RGB)
                        
                        yield frame_count + start_frame, rgb_frame
                        
                        extracted_count += 1
                        pbar.update(1)
                        
                        # Stop if we've reached the maximum number of frames
                        if extracted_count >= self.max_frames:
                            break
                    
                    frame_count += 1
                    
        finally:
            cap.release()
    
    def extract_frames_as_images(self, video_path: str, start_time: float = 0, 
                                end_time: Optional[float] = None) -> List[Tuple[int, Image.Image]]:
        """
        Extract frames from a video and return as PIL Images.
        
        Args:
            video_path: Path to the video file
            start_time: Start time in seconds
            end_time: End time in seconds (None for entire video)
            
        Returns:
            List of tuples containing (frame_number, PIL_Image)
        """
        frames = []
        
        for frame_num, frame_array in self.extract_frames(video_path, start_time, end_time):
            pil_image = Image.fromarray(frame_array)
            frames.append((frame_num, pil_image))
            
        return frames
    
    def save_frame(self, frame: np.ndarray, output_path: str) -> None:
        """
        Save a frame to disk.
        
        Args:
            frame: Frame array (RGB format)
            output_path: Path to save the frame
        """
        # Convert RGB to BGR for OpenCV
        bgr_frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(output_path, bgr_frame)
    
    def create_video_summary(self, video_path: str, num_keyframes: int = 10) -> List[Tuple[int, Image.Image]]:
        """
        Create a summary of the video by extracting key frames at regular intervals.
        
        Args:
            video_path: Path to the video file
            num_keyframes: Number of key frames to extract
            
        Returns:
            List of key frames as PIL Images
        """
        video_info = self.get_video_info(video_path)
        total_frames = video_info['frame_count']
        
        # Calculate frame intervals
        interval = max(1, total_frames // num_keyframes)
        
        keyframes = []
        frame_numbers = range(0, total_frames, interval)[:num_keyframes]
        
        cap = cv2.VideoCapture(video_path)
        
        try:
            for frame_num in frame_numbers:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
                ret, frame = cap.read()
                
                if ret:
                    # Resize and convert to RGB
                    resized_frame = cv2.resize(frame, (self.resize_width, self.resize_height))
                    rgb_frame = cv2.cvtColor(resized_frame, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(rgb_frame)
                    
                    keyframes.append((frame_num, pil_image))
                    
        finally:
            cap.release()
            
        return keyframes
