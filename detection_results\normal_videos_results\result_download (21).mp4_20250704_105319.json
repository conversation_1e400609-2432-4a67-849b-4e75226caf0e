{"video_path": "c:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale\\download (21).mp4", "video_info": {"fps": 15.0, "frame_count": 288, "width": 704, "height": 480, "duration_seconds": 19.2, "file_size_mb": 0.4330778121948242}, "processing_info": {"total_keyframes_processed": 15, "keyframe_mode": true, "processing_timestamp": "2025-07-04T10:53:19.470218"}, "detections": [{"image_index": 0, "is_shoplifting": false, "confidence": -0.063404381275177, "shoplifting_probability": 0.5125285983085632, "normal_probability": 0.5759329795837402, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 0, "timestamp": 0.0}, {"image_index": 1, "is_shoplifting": false, "confidence": -0.15714335441589355, "shoplifting_probability": 0.49954771995544434, "normal_probability": 0.6566910743713379, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 19, "timestamp": 1.2666666666666666}, {"image_index": 2, "is_shoplifting": false, "confidence": -0.12906068563461304, "shoplifting_probability": 0.5019681453704834, "normal_probability": 0.6310288310050964, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 38, "timestamp": 2.533333333333333}, {"image_index": 3, "is_shoplifting": false, "confidence": -0.1373995542526245, "shoplifting_probability": 0.4949396848678589, "normal_probability": 0.6323392391204834, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 57, "timestamp": 3.8}, {"image_index": 4, "is_shoplifting": false, "confidence": -0.01820090413093567, "shoplifting_probability": 0.4566953778266907, "normal_probability": 0.47489628195762634, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 76, "timestamp": 5.066666666666666}, {"image_index": 5, "is_shoplifting": false, "confidence": 0.07380807399749756, "shoplifting_probability": 0.4685315787792206, "normal_probability": 0.394723504781723, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone looking at products on shelves", "frame_number": 95, "timestamp": 6.333333333333333}, {"image_index": 6, "is_shoplifting": false, "confidence": 0.050770729780197144, "shoplifting_probability": 0.47464364767074585, "normal_probability": 0.4238729178905487, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "a person walking through a store", "frame_number": 114, "timestamp": 7.6}, {"image_index": 7, "is_shoplifting": false, "confidence": -0.010002195835113525, "shoplifting_probability": 0.5047197341918945, "normal_probability": 0.5147219300270081, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "a person walking through a store", "frame_number": 133, "timestamp": 8.866666666666667}, {"image_index": 8, "is_shoplifting": false, "confidence": -0.056639790534973145, "shoplifting_probability": 0.4572281837463379, "normal_probability": 0.513867974281311, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "a person walking through a store", "frame_number": 152, "timestamp": 10.133333333333333}, {"image_index": 9, "is_shoplifting": false, "confidence": 0.1418839395046234, "shoplifting_probability": 0.4856656789779663, "normal_probability": 0.3437817394733429, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 171, "timestamp": 11.4}, {"image_index": 10, "is_shoplifting": false, "confidence": -0.003979295492172241, "shoplifting_probability": 0.47747868299484253, "normal_probability": 0.48145797848701477, "best_shoplifting_prompt": "a person stealing merchandise from a store", "best_normal_prompt": "someone browsing products casually", "frame_number": 190, "timestamp": 12.666666666666666}, {"image_index": 11, "is_shoplifting": false, "confidence": 0.1742953658103943, "shoplifting_probability": 0.5443394184112549, "normal_probability": 0.3700440526008606, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 209, "timestamp": 13.933333333333334}, {"image_index": 12, "is_shoplifting": false, "confidence": 0.10872983932495117, "shoplifting_probability": 0.5337295532226562, "normal_probability": 0.4249997138977051, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 228, "timestamp": 15.2}, {"image_index": 13, "is_shoplifting": false, "confidence": 0.10808068513870239, "shoplifting_probability": 0.46759623289108276, "normal_probability": 0.35951554775238037, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 247, "timestamp": 16.466666666666665}, {"image_index": 14, "is_shoplifting": false, "confidence": 0.22236862778663635, "shoplifting_probability": 0.587040364742279, "normal_probability": 0.3646717369556427, "best_shoplifting_prompt": "suspicious behavior in a retail store", "best_normal_prompt": "someone browsing products casually", "frame_number": 266, "timestamp": 17.733333333333334}], "analysis": {"total_frames": 15, "suspicious_frames": 0, "suspicion_rate": 0.0, "average_confidence": 0.02027380665143331, "max_confidence": 0.22236862778663635, "min_confidence": -0.15714335441589355, "suspicious_periods": [], "risk_level": "NONE"}, "saved_frames": []}