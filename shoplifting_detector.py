"""
Core Shoplifting Detection System
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import numpy as np
from PIL import Image
from tqdm import tqdm

from clip_model import CLIPShopliftingDetector
from video_processor import VideoProcessor
import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ShopliftingDetectionSystem:
    """
    Main system for detecting shoplifting behavior in videos using CLIP.
    """
    
    def __init__(self, model_name: str = None, device: str = None):
        """
        Initialize the shoplifting detection system.
        
        Args:
            model_name: CLIP model name to use
            device: Device to run the model on
        """
        self.clip_detector = CLIPShopliftingDetector(model_name, device)
        self.video_processor = VideoProcessor()
        
        # Create output directory if it doesn't exist
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
        logger.info("Shoplifting Detection System initialized")
    
    def process_video_batch(self, video_path: str, batch_size: int = None, 
                           start_time: float = 0, end_time: Optional[float] = None) -> Dict:
        """
        Process a video in batches to detect shoplifting behavior.
        
        Args:
            video_path: Path to the video file
            batch_size: Number of frames to process in each batch
            start_time: Start time in seconds
            end_time: End time in seconds
            
        Returns:
            Dictionary containing detection results
        """
        if batch_size is None:
            batch_size = config.BATCH_SIZE
            
        logger.info(f"Processing video: {video_path}")
        
        # Get video information
        video_info = self.video_processor.get_video_info(video_path)
        logger.info(f"Video info: {video_info}")
        
        # Extract frames
        frames_data = self.video_processor.extract_frames_as_images(
            video_path, start_time, end_time
        )
        
        if not frames_data:
            logger.warning("No frames extracted from video")
            return self._create_empty_result(video_path, video_info)
        
        logger.info(f"Extracted {len(frames_data)} frames")
        
        # Process frames in batches
        all_detections = []
        suspicious_frames = []
        
        for i in tqdm(range(0, len(frames_data), batch_size), desc="Processing batches"):
            batch_frames = frames_data[i:i + batch_size]
            frame_numbers = [frame_num for frame_num, _ in batch_frames]
            images = [img for _, img in batch_frames]
            
            # Detect shoplifting in batch
            batch_results = self.clip_detector.detect_shoplifting(images)
            
            # Process batch results
            for j, result in enumerate(batch_results):
                frame_num = frame_numbers[j]
                result['frame_number'] = frame_num
                result['timestamp'] = frame_num / video_info['fps']
                
                all_detections.append(result)
                
                # Save suspicious frames if enabled
                if result['is_shoplifting'] and config.SAVE_DETECTED_FRAMES:
                    suspicious_frames.append((frame_num, images[j], result))
        
        # Analyze results
        analysis = self._analyze_detections(all_detections, video_info)
        
        # Save suspicious frames
        saved_frames = self._save_suspicious_frames(
            suspicious_frames, os.path.basename(video_path)
        )
        
        # Create final result
        result = {
            'video_path': video_path,
            'video_info': video_info,
            'processing_info': {
                'total_frames_processed': len(frames_data),
                'batch_size': batch_size,
                'start_time': start_time,
                'end_time': end_time,
                'processing_timestamp': datetime.now().isoformat()
            },
            'detections': all_detections,
            'analysis': analysis,
            'saved_frames': saved_frames
        }
        
        return result
    
    def process_video_keyframes(self, video_path: str, num_keyframes: int = 20) -> Dict:
        """
        Process only key frames from a video for faster analysis.
        
        Args:
            video_path: Path to the video file
            num_keyframes: Number of key frames to analyze
            
        Returns:
            Dictionary containing detection results
        """
        logger.info(f"Processing key frames from: {video_path}")
        
        # Get video information
        video_info = self.video_processor.get_video_info(video_path)
        
        # Extract key frames
        keyframes = self.video_processor.create_video_summary(video_path, num_keyframes)
        
        if not keyframes:
            logger.warning("No key frames extracted from video")
            return self._create_empty_result(video_path, video_info)
        
        logger.info(f"Extracted {len(keyframes)} key frames")
        
        # Process key frames
        frame_numbers = [frame_num for frame_num, _ in keyframes]
        images = [img for _, img in keyframes]
        
        detections = self.clip_detector.detect_shoplifting(images)
        
        # Add frame information to results
        for i, result in enumerate(detections):
            frame_num = frame_numbers[i]
            result['frame_number'] = frame_num
            result['timestamp'] = frame_num / video_info['fps']
        
        # Analyze results
        analysis = self._analyze_detections(detections, video_info)
        
        # Save suspicious frames
        suspicious_frames = [
            (frame_numbers[i], images[i], result)
            for i, result in enumerate(detections)
            if result['is_shoplifting']
        ]
        
        saved_frames = self._save_suspicious_frames(
            suspicious_frames, os.path.basename(video_path)
        )
        
        # Create final result
        result = {
            'video_path': video_path,
            'video_info': video_info,
            'processing_info': {
                'total_keyframes_processed': len(keyframes),
                'keyframe_mode': True,
                'processing_timestamp': datetime.now().isoformat()
            },
            'detections': detections,
            'analysis': analysis,
            'saved_frames': saved_frames
        }
        
        return result
    
    def _analyze_detections(self, detections: List[Dict], video_info: Dict) -> Dict:
        """
        Analyze detection results to provide summary statistics.
        
        Args:
            detections: List of detection results
            video_info: Video information
            
        Returns:
            Analysis summary
        """
        if not detections:
            return {
                'total_frames': 0,
                'suspicious_frames': 0,
                'suspicion_rate': 0.0,
                'average_confidence': 0.0,
                'max_confidence': 0.0,
                'suspicious_periods': []
            }
        
        suspicious_detections = [d for d in detections if d['is_shoplifting']]
        confidences = [d['confidence'] for d in detections]
        
        # Find suspicious periods (consecutive suspicious frames)
        suspicious_periods = self._find_suspicious_periods(detections, video_info['fps'])
        
        analysis = {
            'total_frames': len(detections),
            'suspicious_frames': len(suspicious_detections),
            'suspicion_rate': len(suspicious_detections) / len(detections),
            'average_confidence': np.mean(confidences),
            'max_confidence': np.max(confidences),
            'min_confidence': np.min(confidences),
            'suspicious_periods': suspicious_periods,
            'risk_level': self._calculate_risk_level(len(suspicious_detections), len(detections))
        }
        
        return analysis
    
    def _find_suspicious_periods(self, detections: List[Dict], fps: float) -> List[Dict]:
        """
        Find periods of consecutive suspicious activity.
        
        Args:
            detections: List of detection results
            fps: Video frame rate
            
        Returns:
            List of suspicious periods
        """
        periods = []
        current_period = None
        
        for detection in detections:
            if detection['is_shoplifting']:
                if current_period is None:
                    current_period = {
                        'start_frame': detection['frame_number'],
                        'start_time': detection['timestamp'],
                        'frames': [detection],
                        'max_confidence': detection['confidence']
                    }
                else:
                    current_period['frames'].append(detection)
                    current_period['max_confidence'] = max(
                        current_period['max_confidence'], 
                        detection['confidence']
                    )
            else:
                if current_period is not None:
                    # End current period
                    last_frame = current_period['frames'][-1]
                    current_period['end_frame'] = last_frame['frame_number']
                    current_period['end_time'] = last_frame['timestamp']
                    current_period['duration'] = (
                        current_period['end_time'] - current_period['start_time']
                    )
                    current_period['frame_count'] = len(current_period['frames'])
                    
                    periods.append(current_period)
                    current_period = None
        
        # Handle case where video ends during suspicious period
        if current_period is not None:
            last_frame = current_period['frames'][-1]
            current_period['end_frame'] = last_frame['frame_number']
            current_period['end_time'] = last_frame['timestamp']
            current_period['duration'] = (
                current_period['end_time'] - current_period['start_time']
            )
            current_period['frame_count'] = len(current_period['frames'])
            periods.append(current_period)
        
        return periods
    
    def _calculate_risk_level(self, suspicious_frames: int, total_frames: int) -> str:
        """
        Calculate risk level based on detection results.
        
        Args:
            suspicious_frames: Number of suspicious frames
            total_frames: Total number of frames
            
        Returns:
            Risk level string
        """
        if total_frames == 0:
            return "UNKNOWN"
        
        suspicion_rate = suspicious_frames / total_frames
        
        if suspicion_rate >= 0.3:
            return "HIGH"
        elif suspicion_rate >= 0.1:
            return "MEDIUM"
        elif suspicion_rate > 0:
            return "LOW"
        else:
            return "NONE"
    
    def _save_suspicious_frames(self, suspicious_frames: List[Tuple], video_name: str) -> List[str]:
        """
        Save suspicious frames to disk.
        
        Args:
            suspicious_frames: List of (frame_num, image, result) tuples
            video_name: Name of the source video
            
        Returns:
            List of saved file paths
        """
        if not config.SAVE_DETECTED_FRAMES or not suspicious_frames:
            return []
        
        saved_files = []
        video_output_dir = os.path.join(config.OUTPUT_DIR, f"frames_{video_name}")
        os.makedirs(video_output_dir, exist_ok=True)
        
        for frame_num, image, result in suspicious_frames:
            filename = f"suspicious_frame_{frame_num:06d}_conf_{result['confidence']:.3f}.jpg"
            filepath = os.path.join(video_output_dir, filename)
            
            # Convert PIL image to numpy array and save
            frame_array = np.array(image)
            self.video_processor.save_frame(frame_array, filepath)
            saved_files.append(filepath)
        
        logger.info(f"Saved {len(saved_files)} suspicious frames to {video_output_dir}")
        return saved_files
    
    def _create_empty_result(self, video_path: str, video_info: Dict) -> Dict:
        """
        Create an empty result structure for videos with no frames.
        
        Args:
            video_path: Path to the video file
            video_info: Video information
            
        Returns:
            Empty result dictionary
        """
        return {
            'video_path': video_path,
            'video_info': video_info,
            'processing_info': {
                'total_frames_processed': 0,
                'processing_timestamp': datetime.now().isoformat()
            },
            'detections': [],
            'analysis': self._analyze_detections([], video_info),
            'saved_frames': []
        }
    
    def save_results(self, results: Dict, output_file: str = None) -> str:
        """
        Save detection results to a JSON file.
        
        Args:
            results: Detection results dictionary
            output_file: Output file path (optional)
            
        Returns:
            Path to the saved file
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            video_name = os.path.basename(results['video_path'])
            output_file = os.path.join(config.OUTPUT_DIR, f"detection_results_{video_name}_{timestamp}.json")
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to: {output_file}")
        return output_file
