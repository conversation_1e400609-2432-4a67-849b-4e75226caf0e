"""
Adaptive detection test with improved multi-level thresholds and analysis.
"""

import os
import json
import numpy as np
from shoplifting_detector import ShopliftingDetectionSystem
from clip_model import CLIPShopliftingDetector
import config

class AdaptiveShopliftingDetector:
    """Enhanced detector with adaptive thresholds and improved analysis."""
    
    def __init__(self):
        self.clip_detector = CLIPShopliftingDetector()
        
    def detect_with_adaptive_thresholds(self, images, video_info=None):
        """
        Detect shoplifting using adaptive multi-level thresholds.
        """
        # Get raw detections with low threshold for maximum sensitivity
        raw_detections = self.clip_detector.detect_shoplifting(images, confidence_threshold=0.1)
        
        # Analyze confidence distribution
        confidences = [d['confidence'] for d in raw_detections]
        shoplifting_probs = [d['shoplifting_probability'] for d in raw_detections]
        normal_probs = [d['normal_probability'] for d in raw_detections]
        
        # Calculate adaptive thresholds based on data distribution
        confidence_mean = np.mean(confidences)
        confidence_std = np.std(confidences)
        
        # Adaptive thresholds
        high_threshold = max(config.CONFIDENCE_THRESHOLD_HIGH, confidence_mean + 1.5 * confidence_std)
        medium_threshold = max(config.CONFIDENCE_THRESHOLD_MEDIUM, confidence_mean + 0.5 * confidence_std)
        low_threshold = max(config.CONFIDENCE_THRESHOLD_LOW, confidence_mean - 0.5 * confidence_std)
        
        # Re-classify detections with adaptive thresholds
        enhanced_detections = []
        for detection in raw_detections:
            confidence = detection['confidence']
            
            # Multi-criteria detection
            is_suspicious = False
            risk_level = "NONE"
            
            # Primary criterion: confidence threshold
            if confidence >= high_threshold:
                is_suspicious = True
                risk_level = "HIGH"
            elif confidence >= medium_threshold:
                is_suspicious = True
                risk_level = "MEDIUM"
            elif confidence >= low_threshold:
                is_suspicious = True
                risk_level = "LOW"
            
            # Secondary criterion: high shoplifting probability with low normal probability
            shop_prob = detection['shoplifting_probability']
            norm_prob = detection['normal_probability']
            
            if shop_prob > 0.85 and norm_prob < 0.6 and not is_suspicious:
                is_suspicious = True
                risk_level = "MEDIUM"
            elif shop_prob > 0.9 and norm_prob < 0.7:
                is_suspicious = True
                risk_level = "HIGH" if risk_level != "HIGH" else risk_level
            
            enhanced_detection = detection.copy()
            enhanced_detection['is_shoplifting'] = is_suspicious
            enhanced_detection['adaptive_risk_level'] = risk_level
            enhanced_detection['thresholds_used'] = {
                'high': high_threshold,
                'medium': medium_threshold, 
                'low': low_threshold
            }
            
            enhanced_detections.append(enhanced_detection)
        
        return enhanced_detections, {
            'adaptive_thresholds': {
                'high': high_threshold,
                'medium': medium_threshold,
                'low': low_threshold
            },
            'confidence_stats': {
                'mean': confidence_mean,
                'std': confidence_std,
                'min': min(confidences),
                'max': max(confidences)
            }
        }

def test_adaptive_detection():
    """Test the adaptive detection system."""
    
    # Test videos
    test_videos = [
        (r"c:\Users\<USER>\Desktop\video pour mohamed\video vol\Magasin_general_286_2025_06_19_11_44_38_0B7xZpMm_shoplifting_def.mp4", "SHOPLIFTING"),
        (r"c:\Users\<USER>\Desktop\video pour mohamed\video vol\Magasin_general_286_2025_05_09_17_35_21_mY7NjOcW_shoplifting_dif.mp4", "SHOPLIFTING"),
        (r"c:\Users\<USER>\Desktop\video pour mohamed\video normale\video1.mp4", "NORMAL"),
        (r"c:\Users\<USER>\Desktop\video pour mohamed\video normale\video2.mp4", "NORMAL")
    ]
    
    print("="*80)
    print("ADAPTIVE DETECTION SYSTEM TEST")
    print("="*80)
    
    detector = AdaptiveShopliftingDetector()
    standard_detector = ShopliftingDetectionSystem()
    
    for video_path, expected_type in test_videos:
        if not os.path.exists(video_path):
            continue
            
        video_name = os.path.basename(video_path)
        print(f"\n📹 {video_name} ({expected_type})")
        print("-" * 60)
        
        try:
            # Standard detection
            standard_results = standard_detector.process_video_keyframes(video_path, num_keyframes=8)
            standard_analysis = standard_results['analysis']
            
            # Adaptive detection
            from video_processor import VideoProcessor
            processor = VideoProcessor()
            frame_data = processor.create_video_summary(video_path, num_keyframes=8)
            frames = [frame for _, frame in frame_data]
            
            adaptive_detections, adaptive_info = detector.detect_with_adaptive_thresholds(frames)
            
            # Analyze adaptive results
            suspicious_adaptive = sum(1 for d in adaptive_detections if d['is_shoplifting'])
            suspicion_rate_adaptive = suspicious_adaptive / len(adaptive_detections)
            max_confidence_adaptive = max(d['confidence'] for d in adaptive_detections)
            
            # Determine adaptive risk level
            if suspicion_rate_adaptive >= 0.4:
                adaptive_risk = "HIGH"
            elif suspicion_rate_adaptive >= 0.2:
                adaptive_risk = "MEDIUM"
            elif suspicion_rate_adaptive > 0:
                adaptive_risk = "LOW"
            else:
                adaptive_risk = "NONE"
            
            print(f"📊 STANDARD DETECTION:")
            print(f"   Risk: {standard_analysis['risk_level']} | "
                  f"Suspicion: {standard_analysis['suspicion_rate']:.1%} | "
                  f"Frames: {standard_analysis['suspicious_frames']}/{standard_analysis['total_frames']}")
            
            print(f"🧠 ADAPTIVE DETECTION:")
            print(f"   Risk: {adaptive_risk} | "
                  f"Suspicion: {suspicion_rate_adaptive:.1%} | "
                  f"Frames: {suspicious_adaptive}/{len(adaptive_detections)}")
            
            print(f"🔧 Adaptive Thresholds: "
                  f"H:{adaptive_info['adaptive_thresholds']['high']:.3f} | "
                  f"M:{adaptive_info['adaptive_thresholds']['medium']:.3f} | "
                  f"L:{adaptive_info['adaptive_thresholds']['low']:.3f}")
            
            # Evaluation
            if expected_type == "SHOPLIFTING":
                standard_correct = standard_analysis['risk_level'] != "NONE"
                adaptive_correct = adaptive_risk != "NONE"
                print(f"✅ Standard: {'CORRECT' if standard_correct else 'MISSED'}")
                print(f"✅ Adaptive: {'CORRECT' if adaptive_correct else 'MISSED'}")
            else:  # NORMAL
                standard_correct = standard_analysis['risk_level'] == "NONE"
                adaptive_correct = adaptive_risk == "NONE"
                print(f"✅ Standard: {'CORRECT' if standard_correct else 'FALSE POSITIVE'}")
                print(f"✅ Adaptive: {'CORRECT' if adaptive_correct else 'FALSE POSITIVE'}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n{'='*80}")
    print("RECOMMENDATIONS:")
    print("="*80)
    print("1. Adaptive thresholds adjust to video content characteristics")
    print("2. Multi-criteria detection reduces false positives")
    print("3. Consider implementing temporal consistency checks")
    print("4. May need dataset-specific threshold calibration")


if __name__ == "__main__":
    test_adaptive_detection()
