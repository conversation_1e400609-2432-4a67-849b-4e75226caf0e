"""
Comprehensive retest with improved threshold settings.
"""

import os
import json
import random
from shoplifting_detector import ShopliftingDetectionSystem

def comprehensive_retest():
    """Test a larger sample with the improved threshold."""
    
    # Sample videos from each dataset
    shoplifting_dir = r"c:\Users\<USER>\Desktop\video pour mohamed\video vol"
    normal_dir = r"c:\Users\<USER>\Desktop\video pour mohamed\video normale"
    
    # Get all videos
    shoplifting_videos = [os.path.join(shoplifting_dir, f) for f in os.listdir(shoplifting_dir) 
                         if f.lower().endswith(('.mp4', '.avi', '.mov'))]
    normal_videos = [os.path.join(normal_dir, f) for f in os.listdir(normal_dir) 
                    if f.lower().endswith(('.mp4', '.avi', '.mov'))]
    
    # Sample 10 videos from each category for testing
    sample_shoplifting = random.sample(shoplifting_videos, min(10, len(shoplifting_videos)))
    sample_normal = random.sample(normal_videos, min(10, len(normal_videos)))
    
    print("="*80)
    print("COMPREHENSIVE RETEST WITH IMPROVED THRESHOLD (0.2)")
    print("="*80)
    
    detector = ShopliftingDetectionSystem()
    
    # Test shoplifting videos
    print(f"\n🔍 Testing {len(sample_shoplifting)} SHOPLIFTING videos:")
    print("-" * 60)
    
    shoplifting_results = []
    for i, video_path in enumerate(sample_shoplifting, 1):
        video_name = os.path.basename(video_path)
        print(f"\n{i:2d}. {video_name[:50]}...")
        
        try:
            results = detector.process_video_keyframes(video_path, num_keyframes=8)
            analysis = results['analysis']
            
            shoplifting_results.append({
                'name': video_name,
                'risk_level': analysis['risk_level'],
                'suspicion_rate': analysis['suspicion_rate'],
                'suspicious_frames': analysis['suspicious_frames'],
                'total_frames': analysis['total_frames'],
                'max_confidence': analysis['max_confidence']
            })
            
            risk_emoji = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟠", "NONE": "🟢"}
            print(f"     {risk_emoji.get(analysis['risk_level'], '⚪')} {analysis['risk_level']} | "
                  f"Suspicion: {analysis['suspicion_rate']:.1%} | "
                  f"Frames: {analysis['suspicious_frames']}/{analysis['total_frames']} | "
                  f"Conf: {analysis['max_confidence']:.3f}")
                  
        except Exception as e:
            print(f"     ❌ Error: {e}")
    
    # Test normal videos
    print(f"\n🛒 Testing {len(sample_normal)} NORMAL videos:")
    print("-" * 60)
    
    normal_results = []
    for i, video_path in enumerate(sample_normal, 1):
        video_name = os.path.basename(video_path)
        print(f"\n{i:2d}. {video_name[:50]}...")
        
        try:
            results = detector.process_video_keyframes(video_path, num_keyframes=8)
            analysis = results['analysis']
            
            normal_results.append({
                'name': video_name,
                'risk_level': analysis['risk_level'],
                'suspicion_rate': analysis['suspicion_rate'],
                'suspicious_frames': analysis['suspicious_frames'],
                'total_frames': analysis['total_frames'],
                'max_confidence': analysis['max_confidence']
            })
            
            risk_emoji = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟠", "NONE": "🟢"}
            print(f"     {risk_emoji.get(analysis['risk_level'], '⚪')} {analysis['risk_level']} | "
                  f"Suspicion: {analysis['suspicion_rate']:.1%} | "
                  f"Frames: {analysis['suspicious_frames']}/{analysis['total_frames']} | "
                  f"Conf: {analysis['max_confidence']:.3f}")
                  
        except Exception as e:
            print(f"     ❌ Error: {e}")
    
    # Calculate statistics
    print(f"\n{'='*80}")
    print("DETECTION PERFORMANCE ANALYSIS")
    print("="*80)
    
    # Shoplifting detection stats
    if shoplifting_results:
        detected_shoplifting = sum(1 for r in shoplifting_results if r['risk_level'] != 'NONE')
        high_risk = sum(1 for r in shoplifting_results if r['risk_level'] == 'HIGH')
        medium_risk = sum(1 for r in shoplifting_results if r['risk_level'] == 'MEDIUM')
        
        print(f"\n📊 SHOPLIFTING VIDEOS ({len(shoplifting_results)} tested):")
        print(f"   ✅ Detected as suspicious: {detected_shoplifting}/{len(shoplifting_results)} ({detected_shoplifting/len(shoplifting_results)*100:.1f}%)")
        print(f"   🔴 High risk: {high_risk}")
        print(f"   🟡 Medium risk: {medium_risk}")
        print(f"   🟢 Missed (classified as NONE): {len(shoplifting_results) - detected_shoplifting}")
        
        avg_suspicion = sum(r['suspicion_rate'] for r in shoplifting_results) / len(shoplifting_results)
        avg_confidence = sum(r['max_confidence'] for r in shoplifting_results) / len(shoplifting_results)
        print(f"   📈 Average suspicion rate: {avg_suspicion:.1%}")
        print(f"   📈 Average max confidence: {avg_confidence:.3f}")
    
    # Normal video stats
    if normal_results:
        false_positives = sum(1 for r in normal_results if r['risk_level'] != 'NONE')
        
        print(f"\n📊 NORMAL VIDEOS ({len(normal_results)} tested):")
        print(f"   ✅ Correctly classified as normal: {len(normal_results) - false_positives}/{len(normal_results)} ({(len(normal_results) - false_positives)/len(normal_results)*100:.1f}%)")
        print(f"   ❌ False positives: {false_positives}")
        
        if false_positives > 0:
            print(f"   ⚠️  False positive videos:")
            for r in normal_results:
                if r['risk_level'] != 'NONE':
                    print(f"      - {r['name'][:40]}... ({r['risk_level']}, {r['suspicion_rate']:.1%})")
    
    # Overall performance
    if shoplifting_results and normal_results:
        total_correct = detected_shoplifting + (len(normal_results) - false_positives)
        total_videos = len(shoplifting_results) + len(normal_results)
        accuracy = total_correct / total_videos * 100
        
        print(f"\n🎯 OVERALL PERFORMANCE:")
        print(f"   Accuracy: {total_correct}/{total_videos} ({accuracy:.1f}%)")
        print(f"   True Positive Rate: {detected_shoplifting}/{len(shoplifting_results)} ({detected_shoplifting/len(shoplifting_results)*100:.1f}%)")
        print(f"   False Positive Rate: {false_positives}/{len(normal_results)} ({false_positives/len(normal_results)*100:.1f}%)")
    
    print(f"\n{'='*80}")


if __name__ == "__main__":
    comprehensive_retest()
