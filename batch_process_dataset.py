"""
Batch processing script for the video dataset.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

from shoplifting_detector import ShopliftingDetectionSystem
from utils import merge_detection_results, create_detection_timeline, create_detection_report
import config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def find_video_files(directory):
    """Find all video files in a directory."""
    video_files = []
    if not os.path.exists(directory):
        logger.error(f"Directory not found: {directory}")
        return video_files
    
    for file in os.listdir(directory):
        file_path = os.path.join(directory, file)
        if os.path.isfile(file_path):
            _, ext = os.path.splitext(file.lower())
            if ext in config.SUPPORTED_VIDEO_FORMATS:
                video_files.append(file_path)
    
    return sorted(video_files)


def process_video_dataset(dataset_name, video_directory, output_subdir):
    """Process all videos in a dataset directory."""
    logger.info(f"Processing {dataset_name} dataset from: {video_directory}")
    
    # Find all video files
    video_files = find_video_files(video_directory)
    if not video_files:
        logger.warning(f"No video files found in {video_directory}")
        return []
    
    logger.info(f"Found {len(video_files)} video files")
    
    # Create output directory for this dataset
    dataset_output_dir = os.path.join(config.OUTPUT_DIR, output_subdir)
    os.makedirs(dataset_output_dir, exist_ok=True)
    
    # Initialize detection system
    detector = ShopliftingDetectionSystem()
    
    all_results = []
    
    for i, video_path in enumerate(video_files, 1):
        video_name = os.path.basename(video_path)
        logger.info(f"Processing video {i}/{len(video_files)}: {video_name}")
        
        try:
            # Process video using keyframe mode for faster processing
            results = detector.process_video_keyframes(video_path, num_keyframes=15)
            
            # Save individual results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = os.path.join(dataset_output_dir, f"result_{video_name}_{timestamp}.json")
            detector.save_results(results, result_file)
            
            # Create timeline visualization
            timeline_file = os.path.join(dataset_output_dir, f"timeline_{video_name}.png")
            create_detection_timeline(results, timeline_file)
            
            # Create HTML report
            report_file = os.path.join(dataset_output_dir, f"report_{video_name}.html")
            create_detection_report(results, report_file)
            
            all_results.append(results)
            
            # Print summary for this video
            analysis = results['analysis']
            logger.info(f"  Risk Level: {analysis['risk_level']}")
            logger.info(f"  Suspicion Rate: {analysis['suspicion_rate']:.2%}")
            logger.info(f"  Suspicious Frames: {analysis['suspicious_frames']}/{analysis['total_frames']}")
            
        except Exception as e:
            logger.error(f"Error processing {video_name}: {e}")
            continue
    
    # Create dataset summary
    if all_results:
        summary = merge_detection_results(all_results)
        summary['dataset_name'] = dataset_name
        summary['video_directory'] = video_directory
        summary['total_videos_processed'] = len(all_results)
        
        # Save summary
        summary_file = os.path.join(dataset_output_dir, f"{dataset_name}_summary.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(f"Dataset summary saved to: {summary_file}")
        
        # Print dataset summary
        print(f"\n{'='*60}")
        print(f"{dataset_name.upper()} DATASET SUMMARY")
        print(f"{'='*60}")
        print(f"Total videos processed: {summary['total_videos_processed']}")
        print(f"Total frames analyzed: {summary['total_frames_processed']}")
        print(f"Overall suspicion rate: {summary['overall_suspicion_rate']:.2%}")
        print(f"Risk distribution:")
        for risk_level, count in summary['risk_distribution'].items():
            print(f"  {risk_level}: {count} videos")
        
        if summary['high_risk_videos']:
            print(f"\nHigh risk videos:")
            for video in summary['high_risk_videos']:
                video_name = os.path.basename(video['path'])
                print(f"  - {video_name} (suspicion: {video['suspicion_rate']:.2%}, confidence: {video['max_confidence']:.3f})")
        
        print(f"{'='*60}")
    
    return all_results


def main():
    """Main function to process both datasets."""
    print("="*60)
    print("BATCH PROCESSING SHOPLIFTING DETECTION DATASETS")
    print("="*60)
    
    # Define dataset paths
    base_path = r"c:\Users\<USER>\Desktop\video pour mohamed"
    datasets = [
        {
            'name': 'normal_videos',
            'path': os.path.join(base_path, 'video normale'),
            'output_subdir': 'normal_videos_results'
        },
        {
            'name': 'shoplifting_videos', 
            'path': os.path.join(base_path, 'video vol'),
            'output_subdir': 'shoplifting_videos_results'
        }
    ]
    
    all_dataset_results = {}
    
    for dataset in datasets:
        try:
            results = process_video_dataset(
                dataset['name'], 
                dataset['path'], 
                dataset['output_subdir']
            )
            all_dataset_results[dataset['name']] = results
            
        except Exception as e:
            logger.error(f"Error processing dataset {dataset['name']}: {e}")
            continue
    
    # Create overall comparison report
    if all_dataset_results:
        print(f"\n{'='*60}")
        print("OVERALL COMPARISON")
        print(f"{'='*60}")
        
        for dataset_name, results in all_dataset_results.items():
            if results:
                summary = merge_detection_results(results)
                print(f"\n{dataset_name.upper()}:")
                print(f"  Videos: {len(results)}")
                print(f"  Overall suspicion rate: {summary['overall_suspicion_rate']:.2%}")
                print(f"  High risk videos: {summary['risk_distribution']['HIGH']}")
                print(f"  Medium risk videos: {summary['risk_distribution']['MEDIUM']}")
        
        # Save overall comparison
        comparison_file = os.path.join(config.OUTPUT_DIR, "dataset_comparison.json")
        comparison_data = {
            'processing_timestamp': datetime.now().isoformat(),
            'datasets': {}
        }
        
        for dataset_name, results in all_dataset_results.items():
            if results:
                comparison_data['datasets'][dataset_name] = merge_detection_results(results)
        
        with open(comparison_file, 'w') as f:
            json.dump(comparison_data, f, indent=2, default=str)
        
        logger.info(f"Overall comparison saved to: {comparison_file}")
        print(f"\nDetailed results saved in: {config.OUTPUT_DIR}")
        print("Check the individual video reports and timelines for more details.")


if __name__ == "__main__":
    main()
